using System.Collections.Generic;
using DG.Tweening;
using GenericEventSystem;
using UnityEngine;
using UnityEngine.UIElements;

[UxmlElement]
public partial class CharacterDeckUI : VisualElement {
    private VisualTreeAsset characterTileAsset;

    private const string topStackId = "TopStack";
    private const string bottomStackId = "BottomStack";

    private VisualElement _topStack;
    private VisualElement _bottomStack;
    private DeckSelectedCharacterContainer _selectedCharacterContainer;

    private VisualElement TopStack =>
        _topStack ??= this.Q<VisualElement>(topStackId);

    private VisualElement BottomStack =>
        _bottomStack ??= this.Q<VisualElement>(bottomStackId);

    private DeckSelectedCharacterContainer SelectedCharacterContainer =>
        _selectedCharacterContainer ??= this.Q<DeckSelectedCharacterContainer>();

    private CharacterDeckTileUI selectedTile;
    private Dictionary<Character, CharacterDeckTileUI> characterTiles = new Dictionary<Character, CharacterDeckTileUI>();
    private Sequence scrollSequence;

    public CharacterDeckUI() { } 

    public void Init(VisualTreeAsset tileAsset) {
        characterTileAsset = tileAsset;
        SelectedCharacterContainer.Init();
        List<Character> chars = CharactersBucket.GetUnlockedCharacters();

        AddUnlockedCharacters(chars);
        SubscribeToEvents();
    }

    private void SubscribeToEvents() {
        EventCoordinator.StartListening(EventName.Character.Unlock(), OnCharUnlock);
        EventCoordinator.StartListening(EventName.Character.NextSelected(), OnNextSelected);
        this.RegisterCallback<WheelEvent>(OnScroll);
    }

    private void OnCharUnlock(GameMessage msg) {
        AddCharacter(msg.character);
    }

    private void OnNextSelected(GameMessage msg) {
        Character targetCharacter = msg.character;
        if (selectedTile != null && targetCharacter == selectedTile.character) return;
        CharacterDeckTileUI targetTile = characterTiles[targetCharacter];
        if (targetTile == null) {
            Debug.Log($"Character {targetCharacter.Name} doesn't exist in deck");
            return;
        }
        ScrollToNextSelectedCharacter(targetTile);
    }

    void OnScroll(WheelEvent evt) {
        if (evt.delta.y == 0) return;

        var targetTile = GetFirstCharacterTileInStack(evt.delta.y < 0 ? TopStack : BottomStack);
        if (targetTile != null) {
            ScrollToNextSelectedCharacter(targetTile);
        }
    }

    private CharacterDeckTileUI GetFirstCharacterTileInStack(VisualElement stack) {
        if (stack == null || stack.childCount == 0) return null;

        var tile = stack.ElementAt(0) as CharacterDeckTileUI;
        if (tile == null) {
            Debug.LogError($"Stack '{stack.name}' contains non-CharacterDeckTileUI elements.");
        }

        return tile;
    }

    private void ScrollToNextSelectedCharacter(CharacterDeckTileUI targetTile) {
        if (selectedTile != null && selectedTile.character == targetTile.character) return;
        VisualElement fromStack = targetTile.parent;
        if (fromStack == null) return;
        VisualElement toStack = (fromStack == TopStack) ? BottomStack : TopStack;
        scrollSequence?.Kill();

        scrollSequence = DOTween.Sequence();
        CharacterDeckTileUI tile = GetFirstCharacterTileInStack(fromStack);
        if (tile == null) return;

        if (selectedTile != null) {
            toStack.Insert(0, selectedTile);
            RefreshStackStyles(toStack);
        }
        selectedTile = tile;
        EventCoordinator.TriggerEvent(EventName.Character.Select(), GameMessage.Write().WithCharacter(tile.character));

        for (int i = 0; i < fromStack.childCount; i++) {
            CharacterDeckTileUI t = (CharacterDeckTileUI)fromStack.ElementAt(i);
            if (i == 0) {
                t.Vanish();
            }
            else {
                t.SetNewTileHeight(i - 1);
            }
        }
        scrollSequence.AppendInterval(.1f)
        .OnComplete(() => {
            if (!fromStack.Contains(selectedTile)) return;
            fromStack.Remove(selectedTile);
        })
        .AppendCallback(() => ScrollToNextSelectedCharacter(targetTile))
        .OnKill(() => {
            if (!fromStack.Contains(selectedTile)) return;
            fromStack.Remove(selectedTile);
        })
        .Play();
    }

    private void AddUnlockedCharacters(List<Character> chars) {
        TopStack.Clear();
        BottomStack.Clear();

        if (chars.Count == 0) return;

        foreach (Character character in chars) {
            AddCharacter(character);
        }
    }

    private void AddCharacter(Character character) {
        if (characterTileAsset == null) {
            Debug.LogError("CharacterDeckUI: characterTileAsset is null. Make sure to call Init() first.");
            return;
        }
        
        TemplateContainer container = characterTileAsset.Instantiate();
        CharacterDeckTileUI tile = container.Q<CharacterDeckTileUI>();
        tile.Init(character);
        characterTiles[character] = tile;
        TopStack.Insert(0, tile);
        EventCoordinator.TriggerEvent(EventName.Character.NextSelected(), GameMessage.Write().WithCharacter(character));
    }

    private void RefreshStackStyles(VisualElement stack) {
        for (int i = 0; i < stack.childCount; i++) {
            CharacterDeckTileUI t = (CharacterDeckTileUI)stack.ElementAt(i);
            t.SetNewTileHeight(i);
        }
    }

    public void Cleanup() {
        EventCoordinator.StopListening(EventName.Character.Unlock(), OnCharUnlock);
        EventCoordinator.StopListening(EventName.Character.NextSelected(), OnNextSelected);
        this.UnregisterCallback<WheelEvent>(OnScroll);
        scrollSequence?.Kill();
    }
}
