
using GenericEventSystem;
using UnityEngine;
using UnityEngine.UIElements;

public class GameplayUI : MonoBehaviour {
    [SerializeField] VisualTreeAsset characterTileAsset;

    private UIDocument uiDocument => GetComponent<UIDocument>();
    private VisualElement _root;
    private VisualElement root => _root ??= uiDocument.rootVisualElement;
    private CharacterDeckUI _characterDeck;

    private CharacterDeckUI CharacterDeckElement =>
        _characterDeck ??= root.Q<CharacterDeckUI>("CharacterDeckUI");

    void Start() {
        uiDocument.enabled = true;
        root.style.display = DisplayStyle.None;
        GenericEventSystem.EventCoordinator.StartListening(EventName.World.InitialDialogueEnded(), OnStartMission);
    }

    private void OnStartMission(GameMessage msg) {
        root.style.display = DisplayStyle.Flex;

        if (CharacterDeckElement != null && characterTileAsset != null) {
            CharacterDeckElement.Init(characterTileAsset);
        }
    }

    private void OnDestroy() {
        if (CharacterDeckElement != null) {
            CharacterDeckElement.Cleanup();
        }
    }
}
